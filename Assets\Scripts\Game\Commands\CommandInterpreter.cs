// AnimationSystem/Commands/CommandInterpreter.cs
using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Newtonsoft.Json;

namespace Game.CommandSystem
{
    /// <summary>
    /// Interprets command data and creates executable coroutine commands.
    /// </summary>
    public class CommandInterpreter
    {
        /// <summary>
        /// Parses a JSON string into a list of coroutine commands.
        /// </summary>
        /// <param name="json">JSON string representing a command sequence.</param>
        /// <returns>List of parsed coroutine commands.</returns>
        public List<ICoroutineCommand> Parse(string json)
        {
            try
            {
                var sequenceData = JsonConvert.DeserializeObject<CommandSequenceData>(json);
                if (sequenceData?.commands == null)
                    return new List<ICoroutineCommand>();
                return sequenceData.commands.Select(CreateCommandFromData).Where(cmd => cmd != null).ToList();
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to parse command sequence. Error: {e}");
                return new List<ICoroutineCommand>();
            }
        }

        private ICoroutineCommand CreateCommandFromData(CommandData data)
        {
            try
            {
                switch (data.type)
                {
                    case CommandType.Move:
                        var moveParams = (MoveParams)data.parameters;
                        return new MoveCommand(moveParams);
                    case CommandType.Dialogue:
                        var dialogueParams = (DialogueParams)data.parameters;
                        return new DialogueCommand(dialogueParams);
                    case CommandType.CameraFocus:
                        var focusParams = (FocusParams)data.parameters;
                        return new FocusCommand(focusParams);
                    case CommandType.Rotate:
                        var rotateParams = (RotateParams)data.parameters;
                        return new RotateCommand(rotateParams);
                    case CommandType.Scale:
                        var scaleParams = (ScaleParams)data.parameters;
                        return new ScaleCommand(scaleParams);
                    case CommandType.Create:
                        var createParams = (CreateParams)data.parameters;
                        return new CreateCommand(createParams);
                    case CommandType.Destroy:
                        var destroyParams = (DestroyParams)data.parameters;
                        return new DestroyCommand(destroyParams);
                    case CommandType.Composite:
                        var children = data.commands?.Select(CreateCommandFromData).Where(cmd => cmd != null).ToList() ?? new List<ICoroutineCommand>();
                        return new CompositeCommand(children);
                    case CommandType.PlayAnimation:
                        var playAnimationParams = (PlayAnimationParams)data.parameters;
                        return new PlayAnimationCommand(playAnimationParams);
                    case CommandType.PlaySound:
                        var playSoundParams = (PlaySoundParams)data.parameters;
                        return new PlaySoundCommand(playSoundParams);
                    case CommandType.CenterCamera:
                        var centerCameraParams = (CenterCameraParams)data.parameters;
                        return new CenterCameraCommand(centerCameraParams);
                    default:
                        Debug.LogWarning($"Command type '{data.type}' is not implemented in interpreter.");
                        return null;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to create command '{data.type}'. Error: {e.Message}");
                return null;
            }
        }
    }
}