using Game.CommandSystem;
using SmartVertex.Tools;
using UnityEngine;
using UnityEngine.Events;
using System;
using Unity.Cinemachine;
using System.Collections;
using System.Collections.Generic;
using Game.Interfaces;

namespace Game.Managers
{
    /// <summary>
    /// Manages camera behavior and transitions in the game.
    /// </summary>
    public class CameraManager : Singleton<CameraManager>
    {
        /// <summary>
        /// Maps focus speed settings to damping values.
        /// </summary>
        [Serializable]
        public class FocusSpeedMapping
        {
            [Tooltip("The focus speed setting")]
            public FocusSpeed speed;

            [Tooltip("The damping value for this speed")]
            public float damping;
        }

        /// <summary>
        /// Maps shake types to amplitude and frequency values.
        /// </summary>
        [Serializable]
        public class ShakeTypeMapping
        {
            [Tooltip("The type of camera shake")]
            public ShakeType shakeType;

            [Tooltip("The amplitude of the shake")]
            public float amplitude;

            [Tooltip("The frequency of the shake")]
            public float frequency;
        }

        /// <summary>
        /// Maps focus distances to camera offset vectors.
        /// </summary>
        [Serializable]
        public class FocusDistanceMapping
        {
            [Tooltip("The focus distance setting")]
            public FocusDistance distance;

            [Tooltip("The camera offset vector for this distance")]
            public Vector3 offset;
        }

        /// <summary>
        /// Maps center speeds to damping values.
        /// </summary>
        [Serializable]
        public class CenterSpeedMapping
        {
            [Tooltip("The center speed setting")]
            public CenterSpeed speed;

            [Tooltip("The damping value for this speed")]
            public float damping;
        }

        /// <summary>
        /// Maps center zoom levels to padding multipliers.
        /// </summary>
        [Serializable]
        public class CenterZoomMapping
        {
            [Tooltip("The center zoom level setting")]
            public CenterZoomLevel zoomLevel;

            [Tooltip("The padding multiplier for this zoom level")]
            public float paddingMultiplier;
        }

        [SerializeField]
        [Tooltip("Mappings for different focus speed settings")]
        private FocusSpeedMapping[] focusSpeedMappings;

        [SerializeField]
        [Tooltip("Mappings for different camera shake types")]
        private ShakeTypeMapping[] shakeTypeMappings;

        [SerializeField]
        [Tooltip("Mappings for different focus distance settings")]
        private FocusDistanceMapping[] focusDistanceMappings;

        [SerializeField]
        [Tooltip("Mappings for different center speed settings")]
        private CenterSpeedMapping[] centerSpeedMappings;

        [SerializeField]
        [Tooltip("Mappings for different center zoom level settings")]
        private CenterZoomMapping[] centerZoomMappings;

        [SerializeField]
        [Tooltip("The main Cinemachine camera")]
        private CinemachineCamera cinemachineCamera;

        private CinemachineHardLockToTarget hardLockToTarget;
        private CinemachineBasicMultiChannelPerlin noise;
        private CinemachineCameraOffset offset;

        /// <inheritdoc/>
        protected override void Awake()
        {
            base.Awake();

            if (cinemachineCamera == null)
            {
                Debug.LogError($"[{nameof(CameraManager)}] CinemachineCamera reference is missing!");
                return;
            }

            hardLockToTarget = cinemachineCamera.GetComponent<CinemachineHardLockToTarget>();
            noise = cinemachineCamera.GetComponent<CinemachineBasicMultiChannelPerlin>();
            offset = cinemachineCamera.GetComponent<CinemachineCameraOffset>();
        }

        /// <summary>
        /// Sets the camera focus transition speed.
        /// </summary>
        /// <param name="speed">The desired focus speed setting.</param>
        public void SetFocusSpeed(FocusSpeed speed)
        {
            var mapping = Array.Find(focusSpeedMappings, m => m.speed == speed);
            if (mapping != null)
            {
                hardLockToTarget.Damping = mapping.damping;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Focus speed mapping for {speed} not found.");
            }
        }

        /// <summary>
        /// Sets the camera shake parameters.
        /// </summary>
        /// <param name="shakeType">The desired shake type setting.</param>
        public void SetShakeType(ShakeType shakeType)
        {
            var mapping = Array.Find(shakeTypeMappings, m => m.shakeType == shakeType);
            if (mapping != null)
            {
                noise.AmplitudeGain = mapping.amplitude;
                noise.FrequencyGain = mapping.frequency;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Shake type mapping for {shakeType} not found.");
            }
        }

        /// <summary>
        /// Sets the camera distance from the target.
        /// </summary>
        /// <param name="distance">The desired focus distance setting.</param>
        public void SetDistance(FocusDistance distance)
        {
            var mapping = Array.Find(focusDistanceMappings, m => m.distance == distance);
            if (mapping != null)
            {
                offset.Offset = mapping.offset;
            }
            else
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Focus distance mapping for {distance} not found.");
            }
        }

        /// <summary>
        /// Initiates a camera focus operation on a specified target.
        /// </summary>
        /// <param name="targetId">The ID of the target object to focus on.</param>
        /// <param name="speed">The speed of the focus transition.</param>
        /// <param name="shakeType">The type of camera shake to apply.</param>
        /// <param name="distance">The distance to maintain from the target.</param>
        /// <param name="onComplete">Optional callback when focus completes.</param>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        public IEnumerator FocusOnTarget(
            string targetId,
            FocusSpeed speed = FocusSpeed.Instant,
            ShakeType shakeType = ShakeType.Light,
            FocusDistance distance = FocusDistance.Medium,
            UnityAction onComplete = null)
        {
            if (string.IsNullOrEmpty(targetId))
            {
                Debug.LogError($"[{nameof(CameraManager)}] Target ID cannot be null or empty.");
                yield break;
            }

            SetFocusSpeed(speed);
            SetShakeType(shakeType);
            SetDistance(distance);

            var target = ObjectManager.Instance.FindById(targetId);
            if (target == null)
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Target with ID {targetId} not found.");
                yield break;
            }

            var focusable = target.GetComponentInChildren<IFocusable>();
            if (focusable == null)
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] Target {targetId} does not implement IFocusable.");
                yield break;
            }

            var cameraTarget = new CameraTarget
            {
                TrackingTarget = focusable.FocusTransform
            };
            cinemachineCamera.Target = cameraTarget;

            yield return new WaitForSeconds(hardLockToTarget.Damping);

            onComplete?.Invoke();
        }

        /// <summary>
        /// Centers the camera to show all characters or objects in view, optionally inactive. If no objects are specified, all characters are included.
        /// </summary>
        /// <param name="centerSpeed">The speed of the centering transition.</param>
        /// <param name="zoomLevel">The zoom level for the centered view.</param>
        /// <param name="includeObjects">Optional specific object IDs to include. If null or empty, all characters are included.</param>
        /// <param name="includeInactiveCharacters">Whether to include inactive characters, and inactive objects if specified.</param>
        /// <param name="onComplete">Optional callback when centering completes.</param>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        public IEnumerator CenterCamera(
            CenterSpeed centerSpeed = CenterSpeed.Instant,
            CenterZoomLevel zoomLevel = CenterZoomLevel.Comfortable,
            string[] includeObjects = null,
            bool includeInactiveCharacters = false,
            UnityAction onComplete = null)
        {
            // Get all objects to include in centering calculation
            var objectsToCenter = GetObjectsToCenter(includeObjects, includeInactiveCharacters);

            if (objectsToCenter.Count == 0)
            {
                Debug.LogWarning($"[{nameof(CameraManager)}] No objects found to center camera on.");
                yield break;
            }

            // Calculate bounding box of all objects
            var bounds = CalculateBounds(objectsToCenter);

            // Get center speed damping
            var speedMapping = Array.Find(centerSpeedMappings, m => m.speed == centerSpeed);
            float damping = speedMapping?.damping ?? 0f;

            // Get zoom level padding
            var zoomMapping = Array.Find(centerZoomMappings, m => m.zoomLevel == zoomLevel);
            float paddingMultiplier = zoomMapping?.paddingMultiplier ?? 1.2f;

            // Calculate camera position and size
            var centerPosition = bounds.center;
            var requiredSize = CalculateRequiredCameraSize(bounds, paddingMultiplier);

            // Create a temporary target for centering
            var tempTarget = new GameObject("TempCenterTarget");
            tempTarget.transform.position = centerPosition;

            try
            {
                // Set camera target to center position
                var cameraTarget = new CameraTarget
                {
                    TrackingTarget = tempTarget.transform
                };
                cinemachineCamera.Target = cameraTarget;

                // Adjust camera size/field of view
                yield return AdjustCameraSize(requiredSize, damping);

                // Wait for transition to complete
                if (damping > 0)
                {
                    yield return new WaitForSeconds(damping);
                }

                onComplete?.Invoke();
            }
            finally
            {
                // Clean up temporary target
                if (tempTarget != null)
                {
                    Destroy(tempTarget);
                }
            }
        }

        /// <summary>
        /// Gets all objects that should be included in the centering calculation, optionally inactive. If no objects are specified, all characters are included.
        /// </summary>
        /// <param name="includeObjects">Specific object IDs to include. If null or empty, all characters are included.</param>
        /// <param name="includeInactiveCharacters">Whether to include inactive characters, and inactive objects if specified.</param>
        /// <returns>List of GameObjects to center on.</returns>
        private List<GameObject> GetObjectsToCenter(string[] includeObjects, bool includeInactiveCharacters)
        {
            if (includeObjects != null && includeObjects.Length > 0)
            {
                // Include specific objects, optionally inactive
                return ObjectManager.Instance.GetAllByIds(includeObjects, includeInactiveCharacters);
            }
            else
            {
                // Include all characters, optionally inactive
                return ObjectManager.Instance.GetAllCharacters(includeInactiveCharacters);
            }
        }

        /// <summary>
        /// Calculates the bounding box that contains all specified objects.
        /// </summary>
        /// <param name="objects">Objects to calculate bounds for.</param>
        /// <returns>Bounding box containing all objects.</returns>
        private Bounds CalculateBounds(List<GameObject> objects)
        {
            if (objects.Count == 0)
                return new Bounds();

            var bounds = new Bounds(objects[0].transform.position, Vector3.zero);

            foreach (var obj in objects)
            {
                // Get renderer bounds if available, otherwise use transform position
                var renderers = obj.GetComponentsInChildren<Renderer>();
                if (renderers.Length > 0)
                {
                    foreach (var renderer in renderers)
                    {
                        bounds.Encapsulate(renderer.bounds);
                    }
                }
                else
                {
                    bounds.Encapsulate(obj.transform.position);
                }
            }

            return bounds;
        }

        /// <summary>
        /// Calculates the required camera size to fit the bounds with padding.
        /// </summary>
        /// <param name="bounds">Bounds to fit in camera view.</param>
        /// <param name="paddingMultiplier">Padding multiplier for zoom level.</param>
        /// <returns>Required camera size.</returns>
        private float CalculateRequiredCameraSize(Bounds bounds, float paddingMultiplier)
        {
            // For orthographic cameras, use orthographic size
            // For perspective cameras, this would need different calculation
            var size = Mathf.Max(bounds.size.x, bounds.size.y) * paddingMultiplier * 0.5f;
            return Mathf.Max(size, 1f); // Minimum size of 1
        }

        /// <summary>
        /// Adjusts the camera size/field of view to fit the required size.
        /// </summary>
        /// <param name="requiredSize">Required camera size.</param>
        /// <param name="damping">Transition damping.</param>
        /// <returns>An IEnumerator for coroutine execution.</returns>
        private IEnumerator AdjustCameraSize(float requiredSize, float damping)
        {
            var lens = cinemachineCamera.Lens;

            if (lens.Orthographic)
            {
                // For orthographic cameras, adjust orthographic size
                if (damping > 0)
                {
                    var startSize = lens.OrthographicSize;
                    var elapsedTime = 0f;

                    while (elapsedTime < damping)
                    {
                        elapsedTime += Time.deltaTime;
                        var t = elapsedTime / damping;
                        lens.OrthographicSize = Mathf.Lerp(startSize, requiredSize, t);
                        cinemachineCamera.Lens = lens;
                        yield return null;
                    }
                }

                lens.OrthographicSize = requiredSize;
                cinemachineCamera.Lens = lens;
            }
            else
            {
                // For perspective cameras, we would adjust field of view or distance
                // This is more complex and would require distance calculation
                Debug.LogWarning($"[{nameof(CameraManager)}] Perspective camera centering not fully implemented.");
            }
        }
    }
}