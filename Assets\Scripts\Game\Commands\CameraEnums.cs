using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Defines different speeds for camera focus transitions.
    /// </summary>
    public enum FocusSpeed
    {
        Instant = 0,
        Fast = 1,
        Slow = 2
    }

    /// <summary>
    /// Defines different camera distance presets from the target.
    /// </summary>
    public enum FocusDistance
    {
        Close = 0,
        Medium = 1,
        Far = 2
    }

    /// <summary>
    /// Defines different types of camera shake intensities.
    /// </summary>
    public enum ShakeType
    {
        Stable = 0,
        Light = 1,
        Medium = 2,
        Heavy = 3
    }

    /// <summary>
    /// Defines different speeds for camera centering transitions.
    /// </summary>
    public enum CenterSpeed
    {
        Instant = 0,
        Fast = 1,
        Slow = 2
    }

    /// <summary>
    /// Defines different zoom levels for the centered camera view.
    /// </summary>
    public enum CenterZoomLevel
    {
        /// <summary>Tight fit around all characters.</summary>
        Tight = 0,
        /// <summary>Comfortable view with some padding.</summary>
        Comfortable = 1,
        /// <summary>Wide view with generous padding.</summary>
        Wide = 2
    }
}