using System;
using System.Collections;
using Game.Managers;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to center the camera and bring all characters into view.
    /// </summary>
    public class CenterCameraCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the center camera command.</summary>
        public CenterCameraParams Parameters { get; }

        /// <summary>Creates a new CenterCameraCommand.</summary>
        /// <param name="parameters">Center camera parameters.</param>
        public CenterCameraCommand(CenterCameraParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            yield return CameraManager.Instance.CenterCamera(
                Parameters.centerSpeed,
                Parameters.zoomLevel,
                Parameters.includeObjects,
                Parameters.includeInactiveCharacters
            );
        }
    }

    /// <summary>Parameters for CenterCamera command.</summary>
    [Serializable]
    public struct CenterCameraParams
    {
        /// <summary>
        /// The speed at which the camera transitions to the centered position.
        /// </summary>
        public CenterSpeed centerSpeed;

        /// <summary>
        /// The zoom level for the centered camera view.
        /// </summary>
        public CenterZoomLevel zoomLevel;

        /// <summary>
        /// Optional array of specific object IDs to include in the centering calculation.
        /// If null or empty, all active characters will be included.
        /// </summary>
        public string[] includeObjects;

        /// <summary>
        /// Whether to include inactive characters in the centering calculation.
        /// </summary>
        public bool includeInactiveCharacters;
    }
}
