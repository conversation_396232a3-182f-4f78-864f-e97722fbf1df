using System;
using System.Collections;
using Game.Managers;
using UnityEngine;

namespace Game.CommandSystem
{
    /// <summary>
    /// Command to center the camera and bring all characters into view.
    /// </summary>
    public class CenterCameraCommand : ICoroutineCommand
    {
        /// <summary>Parameters for the center camera command.</summary>
        public CenterCameraParams Parameters { get; }

        /// <summary>Creates a new CenterCameraCommand.</summary>
        /// <param name="parameters">Center camera parameters.</param>
        public CenterCameraCommand(CenterCameraParams parameters)
        {
            Parameters = parameters;
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            // TODO: Implement camera centering logic
            // This should:
            // 1. Find all active characters in the scene
            // 2. Calculate the bounding box that contains all characters
            // 3. Position and zoom the camera to fit all characters based on zoomLevel
            // 4. Animate the transition based on centerSpeed
            // 5. Optionally include specific objects if includeObjects is specified

            Debug.Log($"CenterCameraCommand executed with speed: {Parameters.centerSpeed}, zoom: {Parameters.zoomLevel}");

            if (Parameters.includeObjects != null && Parameters.includeObjects.Length > 0)
            {
                Debug.Log($"Including specific objects: {string.Join(", ", Parameters.includeObjects)}");
            }

            // Placeholder - replace with actual implementation
            yield return new WaitForSeconds(0.1f);
        }
    }

    /// <summary>Parameters for CenterCamera command.</summary>
    [Serializable]
    public struct CenterCameraParams
    {
        /// <summary>
        /// The speed at which the camera transitions to the centered position.
        /// </summary>
        public CenterSpeed centerSpeed;

        /// <summary>
        /// The zoom level for the centered camera view.
        /// </summary>
        public CenterZoomLevel zoomLevel;

        /// <summary>
        /// Optional array of specific object IDs to include in the centering calculation.
        /// If null or empty, all active characters will be included.
        /// </summary>
        public string[] includeObjects;

        /// <summary>
        /// Whether to include inactive characters in the centering calculation.
        /// </summary>
        public bool includeInactiveCharacters;
    }
}
