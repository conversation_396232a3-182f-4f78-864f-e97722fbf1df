using System.Collections.Generic;
using UnityEngine;
using SmartVertex.Tools;
using Game.CommandSystem;
using System.Linq;

namespace Game.Managers
{
    /// <summary>
    /// Manages creation and tracking of scenes, objects, and characters using Addressables.
    /// </summary>
    public class ObjectManager : Singleton<ObjectManager>
    {
        // Fields
        [SerializeField] private Vector2Int sceneGridSize = new Vector2Int(14, 8);
        [SerializeField] private Vector3 sceneSpawnOffset = new Vector3(7, 0, 0);
        [SerializeField] private Vector2Int sceneGridSafeArea = new Vector2Int(2, 2);

        private readonly Dictionary<string, GameObject> scenes = new();
        private readonly Dictionary<string, GameObject> objects = new();
        private readonly Dictionary<string, GameObject> characters = new();
        private readonly AddressablesHelper addressablesHelper = new AddressablesHelper();
        private readonly Dictionary<string, GameObject> prefabCache = new();

        // Scene tracking structures
        private readonly Dictionary<string, HashSet<string>> sceneObjects = new();
        private readonly Dictionary<string, HashSet<string>> sceneCharacters = new();
        private readonly Dictionary<string, string> objectToScene = new();
        private readonly Dictionary<string, string> characterToScene = new();

        /// <summary>
        /// Gets the grid size for scenes.
        /// </summary>
        public Vector2Int SceneGridSize => sceneGridSize;

        /// <summary>
        /// Gets the spawn offset for scenes.
        /// </summary>
        public Vector3 SceneSpawnOffset => sceneSpawnOffset;

        /// <summary>
        /// Gets the safe area for the scene grid.
        /// </summary>
        public Vector2Int SceneGridSafeArea => sceneGridSafeArea;

        /// <summary>
        /// Generates and caches prefabs for the provided commands.
        /// </summary>
        /// <param name="commands">List of coroutine commands.</param>
        public async Awaitable GenerateCache(List<ICoroutineCommand> commands)
        {
            foreach (var command in commands)
            {
                if (command is CreateCommand createCommand)
                {
                    prefabCache[createCommand.Parameters.prefabAddress] = await addressablesHelper.LoadAssetAsync<GameObject>(createCommand.Parameters.prefabAddress);
                }
                else if (command is CommandSystem.CompositeCommand compositeCommand)
                {
                    await GenerateCache(compositeCommand.Commands);
                }
            }
        }
        /// <summary>
        /// Asynchronously creates a scene GameObject from an addressable prefab and stores it by sceneId.
        /// </summary>
        /// <param name="sceneId">Unique scene identifier.</param>
        /// <param name="prefabAddress">Addressable prefab address.</param>
        public void CreateScene(string sceneId, string prefabAddress)
        {
            if (scenes.ContainsKey(sceneId))
                return;
            var prefab = prefabCache[prefabAddress];
            if (prefab != null)
            {
                var instance = Instantiate(prefab);
                var multiplier = scenes.Count + 1;
                instance.transform.position += new Vector3(
                    sceneSpawnOffset.x * multiplier,
                    sceneSpawnOffset.y * multiplier,
                    sceneSpawnOffset.z * multiplier);
                instance.name = sceneId;
                scenes[sceneId] = instance;

                // Initialize scene tracking
                sceneObjects[sceneId] = new HashSet<string>();
                sceneCharacters[sceneId] = new HashSet<string>();
            }
        }

        /// <summary>
        /// Asynchronously creates an object in a scene at a grid position.
        /// </summary>
        /// <param name="sceneID">Scene identifier.</param>
        /// <param name="objectID">Object identifier.</param>
        /// <param name="prefabAddress">Addressable prefab address.</param>
        /// <param name="gridPosition">Grid position for instantiation.</param>
        /// <summary>
        /// Asynchronously creates an object in a scene at a grid position.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <param name="objectId">Object identifier.</param>
        /// <param name="prefabAddress">Addressable prefab address.</param>
        /// <param name="gridPosition">Grid position for instantiation.</param>
        public void CreateObject(string sceneId, string objectId, string prefabAddress, Vector2Int gridPosition)
        {
            if (objects.ContainsKey(objectId))
                return;
            if (!scenes.TryGetValue(sceneId, out var scene))
                return;
            var safeGridPosition = ClampToSafeArea(gridPosition);
            var prefab = prefabCache[prefabAddress];
            if (prefab != null)
            {
                var worldPosition = GridUtility.GetWorldPosition(scene, sceneGridSize, safeGridPosition, GridUtility.GridPlane.XY);
                var instance = Instantiate(prefab, worldPosition, Quaternion.identity);
                instance.name = objectId;
                objects[objectId] = instance;
                instance.transform.SetParent(scene.transform);

                // Update scene tracking
                sceneObjects[sceneId].Add(objectId);
                objectToScene[objectId] = sceneId;
            }
        }

        /// <summary>
        /// Asynchronously creates a character in a scene at a grid position.
        /// </summary>
        /// <param name="sceneID">Scene identifier.</param>
        /// <param name="characterId">Character identifier.</param>
        /// <param name="prefabAddress">Addressable prefab address.</param>
        /// <param name="gridPosition">Grid position for instantiation.</param>
        /// <summary>
        /// Asynchronously creates a character in a scene at a grid position.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <param name="characterId">Character identifier.</param>
        /// <param name="prefabAddress">Addressable prefab address.</param>
        /// <param name="gridPosition">Grid position for instantiation.</param>
        public void CreateCharacter(string sceneId, string characterId, string prefabAddress, Vector2Int gridPosition)
        {
            if (characters.ContainsKey(characterId))
                return;
            if (!scenes.TryGetValue(sceneId, out var scene))
                return;
            var safeGridPosition = ClampToSafeArea(gridPosition);
            var prefab = prefabCache[prefabAddress];
            if (prefab != null)
            {
                var worldPosition = GridUtility.GetWorldPosition(scene, sceneGridSize, safeGridPosition, GridUtility.GridPlane.XY);
                var instance = Instantiate(prefab, worldPosition, Quaternion.identity);
                instance.name = characterId;
                characters[characterId] = instance;
                instance.transform.SetParent(scene.transform);

                // Update scene tracking
                sceneCharacters[sceneId].Add(characterId);
                characterToScene[characterId] = sceneId;
            }
        }

        /// <summary>
        /// Destroys an object, character, or scene by its ID and removes it from tracking.
        /// </summary>
        /// <param name="id">The identifier for the object, character, or scene.</param>
        /// <summary>
        /// Destroys an object, character, or scene by its ID and removes it from tracking.
        /// </summary>
        /// <param name="id">The identifier for the object, character, or scene.</param>
        public void DestroyObject(string id)
        {
            if (objects.TryGetValue(id, out var obj))
            {
                Destroy(obj);
                objects.Remove(id);

                // Clean up scene tracking
                if (objectToScene.TryGetValue(id, out var sceneId))
                {
                    sceneObjects[sceneId].Remove(id);
                    objectToScene.Remove(id);
                }
                return;
            }
            if (characters.TryGetValue(id, out var character))
            {
                Destroy(character);
                characters.Remove(id);

                // Clean up scene tracking
                if (characterToScene.TryGetValue(id, out var sceneId))
                {
                    sceneCharacters[sceneId].Remove(id);
                    characterToScene.Remove(id);
                }
                return;
            }
            if (scenes.TryGetValue(id, out var scene))
            {
                Destroy(scene);
                scenes.Remove(id);

                // Clean up scene tracking
                if (sceneObjects.TryGetValue(id, out var objectIds))
                {
                    foreach (var objectId in objectIds)
                        objectToScene.Remove(objectId);
                    sceneObjects.Remove(id);
                }
                if (sceneCharacters.TryGetValue(id, out var characterIds))
                {
                    foreach (var characterId in characterIds)
                        characterToScene.Remove(characterId);
                    sceneCharacters.Remove(id);
                }
            }
        }

        /// <summary>
        /// Retrieves an object by its ID.
        /// </summary>
        /// <param name="objectID"></param>
        /// <returns></returns>
        /// <summary>
        /// Retrieves an object by its ID.
        /// </summary>
        /// <param name="objectId">Object identifier.</param>
        /// <returns>The GameObject instance.</returns>
        public GameObject FindObject(string objectId) => objects[objectId];

        /// <summary>
        /// Retrieves a character by its ID.
        /// </summary>
        /// <param name="characterId">Character identifier.</param>
        /// <returns>The GameObject instance.</returns>
        public GameObject FindCharacter(string characterId) => characters[characterId];

        /// <summary>
        /// Retrieves a scene GameObject by its ID.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>The GameObject instance.</returns>
        public GameObject FindScene(string sceneId) => scenes[sceneId];

        /// <summary>
        /// Searches all tracked dictionaries and returns the GameObject by ID, regardless of its type (object, character, or scene).
        /// </summary>
        /// <param name="id">The identifier for the object, character, or scene.</param>
        /// <returns>The GameObject instance if found; otherwise, null.</returns>
        public GameObject FindById(string id)
        {
            if (objects.TryGetValue(id, out var obj))
                return obj;
            if (characters.TryGetValue(id, out var character))
                return character;
            if (scenes.TryGetValue(id, out var scene))
                return scene;
            return null;
        }

        /// <summary>
        /// Gets all object IDs in a specific scene.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>Read-only collection of object IDs in the scene.</returns>
        public IReadOnlyCollection<string> GetObjectIdsInScene(string sceneId)
        {
            return sceneObjects.TryGetValue(sceneId, out var objectIds) ? objectIds : new HashSet<string>();
        }

        /// <summary>
        /// Gets all character IDs in a specific scene.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>Read-only collection of character IDs in the scene.</returns>
        public IReadOnlyCollection<string> GetCharacterIdsInScene(string sceneId)
        {
            return sceneCharacters.TryGetValue(sceneId, out var characterIds) ? characterIds : new HashSet<string>();
        }

        /// <summary>
        /// Gets all object IDs (objects and characters) in a specific scene.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>Read-only collection of all object IDs in the scene.</returns>
        public IReadOnlyCollection<string> GetAllObjectIdsInScene(string sceneId)
        {
            var allObjects = new HashSet<string>();
            if (sceneObjects.TryGetValue(sceneId, out var objectIds))
                allObjects.UnionWith(objectIds);
            if (sceneCharacters.TryGetValue(sceneId, out var characterIds))
                allObjects.UnionWith(characterIds);
            return allObjects;
        }

        /// <summary>
        /// Gets the scene ID that contains the specified object.
        /// </summary>
        /// <param name="objectId">Object identifier.</param>
        /// <returns>Scene ID if found; otherwise, null.</returns>
        public string GetSceneIdForObject(string objectId)
        {
            return objectToScene.TryGetValue(objectId, out var sceneId) ? sceneId : null;
        }

        /// <summary>
        /// Gets the scene ID that contains the specified character.
        /// </summary>
        /// <param name="characterId">Character identifier.</param>
        /// <returns>Scene ID if found; otherwise, null.</returns>
        public string GetSceneIdForCharacter(string characterId)
        {
            return characterToScene.TryGetValue(characterId, out var sceneId) ? sceneId : null;
        }

        /// <summary>
        /// Gets the scene ID that contains the specified object (object or character).
        /// </summary>
        /// <param name="objectId">Object identifier.</param>
        /// <returns>Scene ID if found; otherwise, null.</returns>
        public string GetSceneIdForAnyObject(string objectId)
        {
            return GetSceneIdForObject(objectId) ?? GetSceneIdForCharacter(objectId);
        }

        /// <summary>
        /// Checks if a scene is empty (contains no objects or characters).
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>True if the scene is empty; otherwise, false.</returns>
        public bool IsSceneEmpty(string sceneId)
        {
            var hasObjects = sceneObjects.TryGetValue(sceneId, out var objectIds) && objectIds.Count > 0;
            var hasCharacters = sceneCharacters.TryGetValue(sceneId, out var characterIds) && characterIds.Count > 0;
            return !hasObjects && !hasCharacters;
        }

        /// <summary>
        /// Gets the total number of objects (objects and characters) in a scene.
        /// </summary>
        /// <param name="sceneId">Scene identifier.</param>
        /// <returns>Total object count in the scene.</returns>
        public int GetSceneObjectCount(string sceneId)
        {
            var objectCount = sceneObjects.TryGetValue(sceneId, out var objectIds) ? objectIds.Count : 0;
            var characterCount = sceneCharacters.TryGetValue(sceneId, out var characterIds) ? characterIds.Count : 0;
            return objectCount + characterCount;
        }

        /// <summary>
        /// Gets all scene IDs that have been created.
        /// </summary>
        /// <returns>Read-only collection of all scene IDs.</returns>
        public IReadOnlyCollection<string> GetAllSceneIds()
        {
            return scenes.Keys;
        }

        /// <summary>
        /// Gets all character GameObjects across all scenes.
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive characters.</param>
        /// <returns>List of all character GameObjects.</returns>
        public List<GameObject> GetAllCharacters(bool includeInactive = false)
        {
            var allCharacters = new List<GameObject>();

            foreach (var character in characters.Values)
            {
                if (character != null && (character.activeInHierarchy || includeInactive))
                {
                    allCharacters.Add(character);
                }
            }

            return allCharacters;
        }

        /// <summary>
        /// Gets all object GameObjects across all scenes.
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive objects.</param>
        /// <returns>List of all object GameObjects.</returns>
        public List<GameObject> GetAllObjects(bool includeInactive = false)
        {
            var allObjects = new List<GameObject>();

            foreach (var obj in objects.Values)
            {
                if (obj != null && (obj.activeInHierarchy || includeInactive))
                {
                    allObjects.Add(obj);
                }
            }

            return allObjects;
        }

        /// <summary>
        /// Gets all characters and objects across all scenes.
        /// </summary>
        /// <param name="includeInactive">Whether to include inactive GameObjects.</param>
        /// <returns>List of all GameObjects.</returns>
        public List<GameObject> GetAll(bool includeInactive = false)
        {
            var allGameObjects = new List<GameObject>();
            allGameObjects.AddRange(GetAllCharacters(includeInactive));
            allGameObjects.AddRange(GetAllObjects(includeInactive));
            return allGameObjects;
        }

        /// <summary>
        /// Gets specific characters or objects by their IDs, across all scenes, and optionally inactive.
        /// </summary>
        /// <param name="objectIds">Array of object IDs to retrieve. If null or empty, an empty list is returned.</param>
        /// <param name="includeInactive">Whether to include inactive GameObjects.</param>
        /// <returns>List of found GameObjects.</returns>
        public List<GameObject> GetAllByIds(string[] objectIds, bool includeInactive = false)
        {
            var gameObjects = new List<GameObject>();

            if (objectIds == null || objectIds.Length == 0)
                return gameObjects;

            foreach (var objectId in objectIds)
            {
                var obj = FindById(objectId);
                if (obj != null && (obj.activeInHierarchy || includeInactive))
                {
                    gameObjects.Add(obj);
                }
            }

            return gameObjects;
        }

        /// <summary>
        /// Clamps a grid position to ensure it is within the safe area boundaries.
        /// </summary>
        /// <param name="gridPosition">Original grid position.</param>
        /// <returns>Clamped grid position within safe area.</returns>
        public Vector2Int ClampToSafeArea(Vector2Int gridPosition)
        {
            int minX = sceneGridSafeArea.x + 1;
            int minY = sceneGridSafeArea.y + 1;
            int maxX = sceneGridSize.x - sceneGridSafeArea.x;
            int maxY = sceneGridSize.y - sceneGridSafeArea.y;
            int clampedX = Mathf.Clamp(gridPosition.x, minX, maxX);
            int clampedY = Mathf.Clamp(gridPosition.y, minY, maxY);
            return new Vector2Int(clampedX, clampedY);
        }

        /// <inheritdoc/>
        protected override void OnDestroy()
        {
            base.OnDestroy();
            addressablesHelper.ReleaseAllAssets();
        }
    }
}