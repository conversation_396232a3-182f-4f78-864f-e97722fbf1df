using System;
using System.Collections;
using Game.Managers;

namespace Game.CommandSystem
{
    /// <summary>
    /// Parameters container for the Focus command configuration.
    /// </summary>
    [Serializable]
    public class FocusParams
    {
        /// <summary>
        /// The unique identifier of the target to focus on.
        /// </summary>
        public string targetId;

        /// <summary>
        /// The speed at which the camera transitions to the target.
        /// </summary>
        public CameraSpeed focusSpeed = CameraSpeed.Instant;

        /// <summary>
        /// The type of camera shake to apply during or after focus.
        /// </summary>
        public ShakeType shakeType = ShakeType.Light;

        /// <summary>
        /// The distance preset to maintain from the target.
        /// </summary>
        public CameraDistance focusDistance = CameraDistance.Medium;
    }

    /// <summary>
    /// Command to focus the camera on a specified target with configurable parameters.
    /// </summary>
    public class FocusCommand : ICoroutineCommand
    {
        /// <summary>
        /// Gets the parameters for this focus command.
        /// </summary>
        public FocusParams Parameters { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="FocusCommand"/> class.
        /// </summary>
        /// <param name="parameters">The focus operation parameters.</param>
        public FocusCommand(FocusParams parameters)
        {
            Parameters = parameters ?? throw new ArgumentNullException(nameof(parameters));
        }

        /// <inheritdoc/>
        public IEnumerator Execute()
        {
            yield return CameraManager.Instance.FocusOnTarget(
                Parameters.targetId,
                Parameters.focusSpeed,
                Parameters.shakeType,
                Parameters.focusDistance
            );
        }
    }
}
